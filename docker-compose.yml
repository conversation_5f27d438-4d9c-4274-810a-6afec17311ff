services:
    bot:
        profiles:
            - main
        build:
            context: .
            dockerfile: Dockerfile.bot
        depends_on:
            - db
            - redis
        environment:
            - TOKEN=${TOKEN}
            - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:${POSTGRES_PORT}/${POSTGRES_DB}
            - REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}
        volumes:
            - .:/bot

    dashboard:
        profiles:
            - main
        build:
            context: .
            dockerfile: Dockerfile.dashboard
        depends_on:
            - db
            - redis
        environment:
            - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:${POSTGRES_PORT}/${POSTGRES_DB}
            - REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}
        ports:
            - "8000:8000"
        volumes:
            - .:/dashboard

    db:
        image: postgres:17.5
        restart: always
        environment:
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
            POSTGRES_DB: ${POSTGRES_DB}
        volumes:
            - pgdata:/var/lib/postgresql/data

    redis:
        image: redis:8.0.2
        restart: always

volumes:
    pgdata: